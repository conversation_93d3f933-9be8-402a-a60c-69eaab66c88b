// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://bjqrjcagtpngzcyysnyw.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJqcXJqY2FndHBuZ3pjeXlzbnl3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyNjk0NDIsImV4cCI6MjA3MTg0NTQ0Mn0.DPPjbe6nipv4VFGxNJpYwIFImmyT90G08BLXAt6H2rg";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});