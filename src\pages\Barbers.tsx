import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Scissors, User, DollarSign, Calendar, Plus } from 'lucide-react';

interface BarberStats {
  barber_id: string;
  barber_name: string;
  total_services: number;
  total_revenue: number;
  completed_appointments: number;
}

export default function Barbers() {
  const { data: barbers = [], isLoading } = useQuery({
    queryKey: ['barbers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('role', 'admin')
        .eq('is_active', true)
        .order('full_name');
      
      if (error) throw error;
      return data;
    },
  });

  const { data: barberStats = [], isLoading: isStatsLoading } = useQuery({
    queryKey: ['barber-stats'],
    queryFn: async () => {
      // Get transaction stats per barber
      const { data: transactionStats, error: transError } = await supabase
        .from('transactions')
        .select(`
          barber_id,
          amount,
          service_id,
          services (name)
        `);
      
      if (transError) throw transError;

      // Get appointment stats per barber
      const { data: appointmentStats, error: appError } = await supabase
        .from('appointments')
        .select(`
          barber_id,
          status
        `)
        .eq('status', 'completed');
      
      if (appError) throw appError;

      // Process the stats
      const statsMap = new Map<string, BarberStats>();
      
      // Initialize with barber data
      for (const barber of barbers) {
        statsMap.set(barber.user_id, {
          barber_id: barber.user_id,
          barber_name: barber.full_name,
          total_services: 0,
          total_revenue: 0,
          completed_appointments: 0,
        });
      }

      // Add transaction data
      for (const transaction of transactionStats || []) {
        const existing = statsMap.get(transaction.barber_id);
        if (existing) {
          existing.total_services += 1;
          existing.total_revenue += Number(transaction.amount);
        }
      }

      // Add appointment data
      for (const appointment of appointmentStats || []) {
        const existing = statsMap.get(appointment.barber_id);
        if (existing) {
          existing.completed_appointments += 1;
        }
      }

      return Array.from(statsMap.values());
    },
    enabled: barbers.length > 0,
  });

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Barbers</h1>
          <p className="text-muted-foreground">Manage your salon team and view performance</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Barber
        </Button>
      </div>

      {/* Statistics Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Barbers</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{barbers.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Services</CardTitle>
            <Scissors className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {barberStats.reduce((sum, barber) => sum + barber.total_services, 0)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${barberStats.reduce((sum, barber) => sum + barber.total_revenue, 0).toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Barber Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Barber Performance</CardTitle>
          <CardDescription>View individual barber statistics and performance metrics</CardDescription>
        </CardHeader>
        <CardContent>
          {isStatsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Barber Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Services Completed</TableHead>
                  <TableHead>Appointments</TableHead>
                  <TableHead>Total Revenue</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {barbers.map((barber) => {
                  const stats = barberStats.find(s => s.barber_id === barber.user_id) || {
                    total_services: 0,
                    total_revenue: 0,
                    completed_appointments: 0,
                  };
                  
                  return (
                    <TableRow key={barber.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{barber.full_name}</div>
                          <div className="text-sm text-muted-foreground">
                            {barber.phone || 'No phone number'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={barber.is_active ? "default" : "secondary"}>
                          {barber.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Scissors className="h-4 w-4 text-muted-foreground" />
                          {stats.total_services}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          {stats.completed_appointments}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          ${stats.total_revenue.toFixed(2)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button 
                            variant={barber.is_active ? "destructive" : "default"} 
                            size="sm"
                          >
                            {barber.is_active ? 'Deactivate' : 'Activate'}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
          
          {barbers.length === 0 && (
            <div className="text-center py-8">
              <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No barbers yet</h3>
              <p className="text-muted-foreground mb-4">Start by adding your first team member</p>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Barber
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}